#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
漏洞检出率影响分析工具
分析Trivy和TCSS组件检出差异对漏洞检出率的影响
"""

import json
import re
from typing import Dict, Set, List, Tuple


def parse_trivy_results(trivy_file: str) -> Dict[str, str]:
    """解析Trivy SPDX格式的扫描结果"""
    components = {}

    try:
        with open(trivy_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        packages = data.get('packages', [])

        for package in packages:
            name = package.get('name', '')
            version = package.get('versionInfo', '')

            if package.get('primaryPackagePurpose') == 'CONTAINER':
                continue

            if not name or not version:
                continue

            if ':' in name and name.count(':') == 1:
                name = name.split(':')[1]

            components[name] = version

    except Exception as e:
        print(f"解析Trivy结果时出错: {e}")
        return {}

    return components


def parse_tcss_results(tcss_file: str) -> Dict[str, str]:
    """解析TCSS格式的扫描结果"""
    components = {}

    try:
        with open(tcss_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        if data.get('code') == 0 and data.get('data'):
            scan_data = data['data'][0] if isinstance(data['data'], list) else data['data']
            component_list = scan_data.get('components', [])

            for component in component_list:
                name = component.get('name', '')
                version = component.get('version', '')

                if not name or not version:
                    continue

                components[name] = version

    except Exception as e:
        print(f"解析TCSS结果时出错: {e}")
        return {}

    return components


def parse_vulnerability_report_simple(html_content: str) -> Dict[str, List[Dict]]:
    """
    简单解析HTML漏洞报告（不依赖BeautifulSoup）
    """
    vulnerabilities = {}

    # 使用正则表达式匹配表格行
    # 匹配包含漏洞信息的行
    row_pattern = r'<tr class="severity-(\w+)">(.*?)</tr>'
    rows = re.findall(row_pattern, html_content, re.DOTALL)

    for severity, row_content in rows:
        # 提取包名
        pkg_match = re.search(r'<td class="pkg-name">([^<]+)</td>', row_content)
        # 提取CVE ID
        cve_match = re.search(r'<td>([^<]*CVE-[^<]*)</td>', row_content)
        # 提取版本
        version_match = re.search(r'<td class="pkg-version">([^<]+)</td>', row_content)

        if pkg_match and cve_match:
            pkg_name = pkg_match.group(1).strip()
            cve_id = cve_match.group(1).strip()
            version = version_match.group(1).strip() if version_match else 'unknown'

            if pkg_name not in vulnerabilities:
                vulnerabilities[pkg_name] = []

            vulnerabilities[pkg_name].append({
                'vuln_id': cve_id,
                'severity': severity.upper(),
                'version': version
            })

    return vulnerabilities


def analyze_vulnerability_impact():
    """分析组件检出差异对漏洞检出率的影响"""
    print("=" * 80)
    print("漏洞检出率影响分析报告")
    print("=" * 80)

    # 1. 解析组件差异
    print("\n正在分析组件差异...")
    trivy_components = parse_trivy_results("trivy_result.json")
    tcss_components = parse_tcss_results("tcss_result.json")

    trivy_only = set(trivy_components.keys()) - set(tcss_components.keys())
    tcss_only = set(tcss_components.keys()) - set(trivy_components.keys())

    print(f"仅Trivy检测到的组件: {len(trivy_only)}个")
    print(f"仅TCSS检测到的组件: {len(tcss_only)}个")

    # 2. 解析漏洞报告
    print("\n正在解析Trivy漏洞报告...")
    try:
        with open("/data/workspace/code_snippets/analyze_image_scan_result/ocloud-tcenter-mc-cas-portal.html", 'r', encoding='utf-8') as f:
            html_content = f.read()
        vulnerabilities = parse_vulnerability_report_simple(html_content)
    except Exception as e:
        print(f"解析漏洞报告时出错: {e}")
        return

    print(f"Trivy检测到的漏洞组件: {len(vulnerabilities)}个")

    # 3. 分析仅Trivy检测到的组件中的漏洞
    trivy_only_vulns = {}
    for pkg in trivy_only:
        if pkg in vulnerabilities:
            trivy_only_vulns[pkg] = vulnerabilities[pkg]

    # 4. 统计漏洞
    total_vulns = sum(len(vuln_list) for vuln_list in vulnerabilities.values())
    trivy_only_total_vulns = sum(len(vuln_list) for vuln_list in trivy_only_vulns.values())

    # 5. 输出分析结果
    print(f"\n📊 漏洞统计总览:")
    print(f"  Trivy总共检测到漏洞: {total_vulns}个")
    print(f"  影响组件数量: {len(vulnerabilities)}个")

    print(f"\n⚠️  仅Trivy检测到的组件中的漏洞:")
    print(f"  包含漏洞的组件: {len(trivy_only_vulns)}个")
    print(f"  漏洞总数: {trivy_only_total_vulns}个")

    if trivy_only_vulns:
        print(f"\n  详细漏洞信息:")
        for pkg, vuln_list in trivy_only_vulns.items():
            print(f"    📦 {pkg} (版本: {trivy_components[pkg]}):")
            for vuln in vuln_list:
                print(f"      - {vuln['vuln_id']} [{vuln['severity']}]")

    # 6. 分析OpenIPMI组件
    print(f"\n🔍 OpenIPMI组件漏洞分析:")
    print(f"  从HTML报告可以看到OpenIPMI相关组件存在CVE-2024-42934漏洞")

    openipmi_components = ['OpenIPMI', 'OpenIPMI-libs', 'OpenIPMI-modalias']
    for comp in openipmi_components:
        in_trivy = comp in trivy_components
        in_tcss = comp in tcss_components
        has_vuln = comp in vulnerabilities

        print(f"    {comp}:")
        print(f"      Trivy检测到: {'✅' if in_trivy else '❌'}")
        print(f"      TCSS检测到:  {'✅' if in_tcss else '❌'}")
        print(f"      存在漏洞:    {'⚠️' if has_vuln else '✅'}")

        if has_vuln:
            vuln_info = vulnerabilities[comp][0]  # 取第一个漏洞
            print(f"      漏洞详情: {vuln_info['vuln_id']} [{vuln_info['severity']}]")

    # 7. 计算影响
    if total_vulns > 0:
        missed_vuln_rate = (trivy_only_total_vulns / total_vulns) * 100
        print(f"\n📈 漏洞检出率影响分析:")
        print(f"  如果仅使用TCSS扫描，可能遗漏的漏洞比例: {missed_vuln_rate:.2f}%")
        print(f"  遗漏的漏洞数量: {trivy_only_total_vulns}个")

        if missed_vuln_rate > 0:
            print(f"\n💡 风险评估:")
            if missed_vuln_rate >= 20:
                risk_level = "高风险"
                recommendation = "强烈建议同时使用两种工具进行扫描"
            elif missed_vuln_rate >= 10:
                risk_level = "中等风险"
                recommendation = "建议结合使用两种工具"
            elif missed_vuln_rate >= 5:
                risk_level = "低风险"
                recommendation = "可考虑定期使用Trivy进行补充扫描"
            else:
                risk_level = "极低风险"
                recommendation = "当前差异对安全影响较小"

            print(f"  风险等级: {risk_level}")
            print(f"  建议: {recommendation}")

    # 8. 分析仅Trivy检测到的高风险组件
    print(f"\n🔍 仅Trivy检测到的潜在高风险组件分析:")

    high_risk_components = [
        'fastjson', 'jackson-core', 'jackson-databind', 'gson', 'guava',
        'tomcat-catalina', 'tomcat-coyote', 'jedis', 'commons-daemon'
    ]

    trivy_only_high_risk = []
    for comp in trivy_only:
        for risk_comp in high_risk_components:
            if risk_comp.lower() in comp.lower():
                trivy_only_high_risk.append(comp)
                break

    if trivy_only_high_risk:
        print(f"  发现{len(trivy_only_high_risk)}个仅Trivy检测到的潜在高风险组件:")
        for comp in trivy_only_high_risk:
            version = trivy_components.get(comp, 'unknown')
            print(f"    - {comp} (版本: {version})")

            # 分析具体风险
            if 'fastjson' in comp.lower():
                print(f"      ⚠️ FastJSON历史上存在多个反序列化漏洞")
            elif 'jackson' in comp.lower():
                print(f"      ⚠️ Jackson组件可能存在反序列化漏洞")
            elif 'tomcat' in comp.lower():
                print(f"      ⚠️ Tomcat组件可能存在Web应用安全漏洞")
            elif 'jedis' in comp.lower():
                print(f"      ⚠️ Redis客户端可能存在连接安全问题")
    else:
        print(f"  未发现明显的高风险组件")

    # 9. 结论和建议
    print(f"\n📋 最终结论:")
    print(f"  1. 当前检测到的漏洞(CVE-2024-42934):")
    print(f"     - 影响OpenIPMI相关组件")
    print(f"     - 严重程度: MEDIUM")
    print(f"     - 两个工具都能检测到相关组件")
    print(f"     - 不会因工具差异而遗漏")

    print(f"\n  2. 组件检出差异影响:")
    print(f"     - 仅Trivy检测到: {len(trivy_only)}个组件")
    print(f"     - 仅TCSS检测到: {len(tcss_only)}个组件")
    print(f"     - 当前已知漏洞不受影响")

    if trivy_only_high_risk:
        print(f"     - ⚠️ 但存在{len(trivy_only_high_risk)}个仅Trivy检测到的潜在高风险组件")
        print(f"     - 这些组件可能存在未知漏洞")

    print(f"\n  3. 风险评估:")
    if trivy_only_high_risk:
        print(f"     - 风险等级: 中等风险")
        print(f"     - 原因: 存在仅Trivy检测到的高风险组件")
        print(f"     - 建议: 强烈建议结合使用两种工具")
    else:
        print(f"     - 风险等级: 低风险")
        print(f"     - 原因: 当前漏洞不受工具差异影响")
        print(f"     - 建议: 可考虑定期使用Trivy进行补充扫描")

    print(f"\n  4. 具体建议:")
    print(f"     - 继续使用两种工具进行互补扫描")
    print(f"     - 重点关注仅一方检测到的组件")
    print(f"     - 建立组件漏洞监控机制")
    print(f"     - 定期更新漏洞数据库")


def main():
    """主函数"""
    analyze_vulnerability_impact()
    print("\n分析完成！")


if __name__ == "__main__":
    main()
