<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <style>
      * {
        font-family: Arial, Helvetica, sans-serif;
      }
      h1 {
        text-align: center;
      }
      .group-header th {
        font-size: 200%;
      }
      .sub-header th {
        font-size: 150%;
      }
      table, th, td {
        border: 1px solid black;
        border-collapse: collapse;
        white-space: nowrap;
        padding: .3em;
      }
      table {
        margin: 0 auto;
      }
      .severity {
        text-align: center;
        font-weight: bold;
        color: #fafafa;
      }
      .severity-LOW .severity { background-color: #5fbb31; }
      .severity-MEDIUM .severity { background-color: #e9c600; }
      .severity-HIGH .severity { background-color: #ff8800; }
      .severity-CRITICAL .severity { background-color: #e40000; }
      .severity-UNKNOWN .severity { background-color: #747474; }
      .severity-LOW { background-color: #5fbb3160; }
      .severity-MEDIUM { background-color: #e9c60060; }
      .severity-HIGH { background-color: #ff880060; }
      .severity-CRITICAL { background-color: #e4000060; }
      .severity-UNKNOWN { background-color: #74747460; }
      table tr td:first-of-type {
        font-weight: bold;
      }
      .links a,
      .links[data-more-links=on] a {
        display: block;
      }
      .links[data-more-links=off] a:nth-of-type(1n+5) {
        display: none;
      }
      a.toggle-more-links { cursor: pointer; }
    </style>
    <title>csighub.tencentyun.com/devsecops/ocloud-tcenter-mc-cas-portal:1.0.14948-20230526-145416-fade7d5 (centos 7.4) - Trivy Report - 2025-05-08 14:46:31.557911 +0800 CST m=+47.792776959 </title>
    <script>
      window.onload = function() {
        document.querySelectorAll('td.links').forEach(function(linkCell) {
          var links = [].concat.apply([], linkCell.querySelectorAll('a'));
          [].sort.apply(links, function(a, b) {
            return a.href > b.href ? 1 : -1;
          });
          links.forEach(function(link, idx) {
            if (links.length > 3 && 3 === idx) {
              var toggleLink = document.createElement('a');
              toggleLink.innerText = "Toggle more links";
              toggleLink.href = "#toggleMore";
              toggleLink.setAttribute("class", "toggle-more-links");
              linkCell.appendChild(toggleLink);
            }
            linkCell.appendChild(link);
          });
        });
        document.querySelectorAll('a.toggle-more-links').forEach(function(toggleLink) {
          toggleLink.onclick = function() {
            var expanded = toggleLink.parentElement.getAttribute("data-more-links");
            toggleLink.parentElement.setAttribute("data-more-links", "on" === expanded ? "off" : "on");
            return false;
          };
        });
      };
    </script>
  </head>
  <body>
    <h1>csighub.tencentyun.com/devsecops/ocloud-tcenter-mc-cas-portal:1.0.14948-20230526-145416-fade7d5 (centos 7.4) - Trivy Report - 2025-05-08 14:46:31.558085 +0800 CST m=+47.792950917</h1>
    <table>
      <tr class="group-header"><th colspan="6">centos</th></tr>
      <tr class="sub-header">
        <th>Package</th>
        <th>Vulnerability ID</th>
        <th>Severity</th>
        <th>Installed Version</th>
        <th>Fixed Version</th>
        <th>Links</th>
      </tr>
      <tr class="severity-MEDIUM">
        <td class="pkg-name">OpenIPMI</td>
        <td>CVE-2024-42934</td>
        <td class="severity">MEDIUM</td>
        <td class="pkg-version">2.0.19-15.tl2</td>
        <td></td>
        <td class="links" data-more-links="off">
          <a href="https://access.redhat.com/errata/RHSA-2024:8037">https://access.redhat.com/errata/RHSA-2024:8037</a>
          <a href="https://access.redhat.com/security/cve/CVE-2024-42934">https://access.redhat.com/security/cve/CVE-2024-42934</a>
          <a href="https://bugzilla.redhat.com/2308375">https://bugzilla.redhat.com/2308375</a>
          <a href="https://bugzilla.redhat.com/show_bug.cgi?id=2308375">https://bugzilla.redhat.com/show_bug.cgi?id=2308375</a>
          <a href="https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2024-42934">https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2024-42934</a>
          <a href="https://errata.almalinux.org/9/ALSA-2024-8037.html">https://errata.almalinux.org/9/ALSA-2024-8037.html</a>
          <a href="https://errata.rockylinux.org/RLSA-2024:8037">https://errata.rockylinux.org/RLSA-2024:8037</a>
          <a href="https://linux.oracle.com/cve/CVE-2024-42934.html">https://linux.oracle.com/cve/CVE-2024-42934.html</a>
          <a href="https://linux.oracle.com/errata/ELSA-2024-8037.html">https://linux.oracle.com/errata/ELSA-2024-8037.html</a>
          <a href="https://nvd.nist.gov/vuln/detail/CVE-2024-42934">https://nvd.nist.gov/vuln/detail/CVE-2024-42934</a>
          <a href="https://sourceforge.net/p/openipmi/code/ci/4c129d0540f3578ecc078d8612bbf84b6cd24c87">https://sourceforge.net/p/openipmi/code/ci/4c129d0540f3578ecc078d8612bbf84b6cd24c87</a>
          <a href="https://sourceforge.net/p/openipmi/code/ci/b52e8e2538b2b48ef6b63bff12b5cc9e2d52eff1/">https://sourceforge.net/p/openipmi/code/ci/b52e8e2538b2b48ef6b63bff12b5cc9e2d52eff1/</a>
          <a href="https://sourceforge.net/p/openipmi/mailman/message/58809029/">https://sourceforge.net/p/openipmi/mailman/message/58809029/</a>
          <a href="https://www.cve.org/CVERecord?id=CVE-2024-42934">https://www.cve.org/CVERecord?id=CVE-2024-42934</a>
        </td>
      </tr>
      <tr class="severity-MEDIUM">
        <td class="pkg-name">OpenIPMI-libs</td>
        <td>CVE-2024-42934</td>
        <td class="severity">MEDIUM</td>
        <td class="pkg-version">2.0.19-15.tl2</td>
        <td></td>
        <td class="links" data-more-links="off">
          <a href="https://access.redhat.com/errata/RHSA-2024:8037">https://access.redhat.com/errata/RHSA-2024:8037</a>
          <a href="https://access.redhat.com/security/cve/CVE-2024-42934">https://access.redhat.com/security/cve/CVE-2024-42934</a>
          <a href="https://bugzilla.redhat.com/2308375">https://bugzilla.redhat.com/2308375</a>
          <a href="https://bugzilla.redhat.com/show_bug.cgi?id=2308375">https://bugzilla.redhat.com/show_bug.cgi?id=2308375</a>
          <a href="https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2024-42934">https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2024-42934</a>
          <a href="https://errata.almalinux.org/9/ALSA-2024-8037.html">https://errata.almalinux.org/9/ALSA-2024-8037.html</a>
          <a href="https://errata.rockylinux.org/RLSA-2024:8037">https://errata.rockylinux.org/RLSA-2024:8037</a>
          <a href="https://linux.oracle.com/cve/CVE-2024-42934.html">https://linux.oracle.com/cve/CVE-2024-42934.html</a>
          <a href="https://linux.oracle.com/errata/ELSA-2024-8037.html">https://linux.oracle.com/errata/ELSA-2024-8037.html</a>
          <a href="https://nvd.nist.gov/vuln/detail/CVE-2024-42934">https://nvd.nist.gov/vuln/detail/CVE-2024-42934</a>
          <a href="https://sourceforge.net/p/openipmi/code/ci/4c129d0540f3578ecc078d8612bbf84b6cd24c87">https://sourceforge.net/p/openipmi/code/ci/4c129d0540f3578ecc078d8612bbf84b6cd24c87</a>
          <a href="https://sourceforge.net/p/openipmi/code/ci/b52e8e2538b2b48ef6b63bff12b5cc9e2d52eff1/">https://sourceforge.net/p/openipmi/code/ci/b52e8e2538b2b48ef6b63bff12b5cc9e2d52eff1/</a>
          <a href="https://sourceforge.net/p/openipmi/mailman/message/58809029/">https://sourceforge.net/p/openipmi/mailman/message/58809029/</a>
          <a href="https://www.cve.org/CVERecord?id=CVE-2024-42934">https://www.cve.org/CVERecord?id=CVE-2024-42934</a>
        </td>
      </tr>
      <tr class="severity-MEDIUM">
        <td class="pkg-name">OpenIPMI-modalias</td>
        <td>CVE-2024-42934</td>
        <td class="severity">MEDIUM</td>
        <td class="pkg-version">2.0.19-15.tl2</td>
        <td></td>
        <td class="links" data-more-links="off">
          <a href="https://access.redhat.com/errata/RHSA-2024:8037">https://access.redhat.com/errata/RHSA-2024:8037</a>
          <a href="https://access.redhat.com/security/cve/CVE-2024-42934">https://access.redhat.com/security/cve/CVE-2024-42934</a>
          <a href="https://bugzilla.redhat.com/2308375">https://bugzilla.redhat.com/2308375</a>
          <a href="https://bugzilla.redhat.com/show_bug.cgi?id=2308375">https://bugzilla.redhat.com/show_bug.cgi?id=2308375</a>
          <a href="https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2024-42934">https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2024-42934</a>
          <a href="https://errata.almalinux.org/9/ALSA-2024-8037.html">https://errata.almalinux.org/9/ALSA-2024-8037.html</a>
          <a href="https://errata.rockylinux.org/RLSA-2024:8037">https://errata.rockylinux.org/RLSA-2024:8037</a>
          <a href="https://linux.oracle.com/cve/CVE-2024-42934.html">https://linux.oracle.com/cve/CVE-2024-42934.html</a>
          <a href="https://linux.oracle.com/errata/ELSA-2024-8037.html">https://linux.oracle.com/errata/ELSA-2024-8037.html</a>
          <a href="https://nvd.nist.gov/vuln/detail/CVE-2024-42934">https://nvd.nist.gov/vuln/detail/CVE-2024-42934</a>
          <a href="https://sourceforge.net/p/openipmi/code/ci/4c129d0540f3578ecc078d8612bbf84b6cd24c87">https://sourceforge.net/p/openipmi/code/ci/4c129d0540f3578ecc078d8612bbf84b6cd24c87</a>
          <a href="https://sourceforge.net/p/openipmi/code/ci/b52e8e2538b2b48ef6b63bff12b5cc9e2d52eff1/">https://sourceforge.net/p/openipmi/code/ci/b52e8e2538b2b48ef6b63bff12b5cc9e2d52eff1/</a>
          <a href="https://sourceforge.net/p/openipmi/mailman/message/58809029/">https://sourceforge.net/p/openipmi/mailman/message/58809029/</a>
          <a href="https://www.cve.org/CVERecord?id=CVE-2024-42934">https://www.cve.org/CVERecord?id=CVE-2024-42934</a>
        </td>
      </tr>
    </table>
  </body>
</html>
