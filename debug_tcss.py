#!/usr/bin/env python3
import json

def debug_tcss():
    with open('tcss_result.json', 'r') as f:
        data = json.load(f)
    
    print("Top level keys:", list(data.keys()))
    
    if 'data' in data:
        scan_data = data['data'][0] if isinstance(data['data'], list) else data['data']
        print("Scan data keys:", list(scan_data.keys()))
        
        if 'vuln' in scan_data:
            vuln_data = scan_data['vuln']
            print("Vuln data keys:", list(vuln_data.keys()))
            
            if 'list' in vuln_data:
                vuln_list = vuln_data['list']
                print(f"Total vulnerabilities: {len(vuln_list)}")
                
                if vuln_list:
                    print("First vulnerability keys:", list(vuln_list[0].keys()))
                    print("First vulnerability:")
                    first_vuln = vuln_list[0]
                    print(f"  Component: {first_vuln.get('component', 'N/A')}")
                    print(f"  CVE ID: {first_vuln.get('cve_id', 'N/A')}")
                    print(f"  Level: {first_vuln.get('level', 'N/A')}")
                    print(f"  Version: {first_vuln.get('version', 'N/A')}")

if __name__ == "__main__":
    debug_tcss()
