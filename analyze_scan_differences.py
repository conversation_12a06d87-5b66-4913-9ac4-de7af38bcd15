#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
镜像扫描结果差异分析工具
分析Trivy和TCSS对同一镜像的扫描结果差异
"""

import json
import sys
from collections import defaultdict
from typing import Dict, Set, List, Tuple


def parse_trivy_results(trivy_file: str) -> Dict[str, str]:
    """
    解析Trivy SPDX格式的扫描结果
    返回: {组件名: 版本} 的字典
    """
    components = {}
    
    try:
        with open(trivy_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 从packages数组中提取组件信息
        packages = data.get('packages', [])
        
        for package in packages:
            name = package.get('name', '')
            version = package.get('versionInfo', '')
            
            # 跳过容器镜像本身的记录
            if package.get('primaryPackagePurpose') == 'CONTAINER':
                continue
                
            # 跳过没有名称或版本的包
            if not name or not version:
                continue
                
            # 处理Maven格式的包名 (如 "antlr:antlr")
            if ':' in name and name.count(':') == 1:
                name = name.split(':')[1]  # 取后半部分作为包名
            
            components[name] = version
            
    except Exception as e:
        print(f"解析Trivy结果时出错: {e}")
        return {}
    
    return components


def parse_tcss_results(tcss_file: str) -> Dict[str, str]:
    """
    解析TCSS格式的扫描结果
    返回: {组件名: 版本} 的字典
    """
    components = {}
    
    try:
        with open(tcss_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 从data[0].components数组中提取组件信息
        if data.get('code') == 0 and data.get('data'):
            scan_data = data['data'][0] if isinstance(data['data'], list) else data['data']
            component_list = scan_data.get('components', [])
            
            for component in component_list:
                name = component.get('name', '')
                version = component.get('version', '')
                
                # 跳过没有名称或版本的组件
                if not name or not version:
                    continue
                
                components[name] = version
                
    except Exception as e:
        print(f"解析TCSS结果时出错: {e}")
        return {}
    
    return components


def normalize_version(version: str) -> str:
    """
    标准化版本号，去除epoch等前缀
    """
    if not version:
        return ""
    
    # 处理epoch格式 (如 "1:7.5-21.tl2" -> "7.5-21.tl2")
    if ':' in version:
        parts = version.split(':', 1)
        if len(parts) == 2 and parts[0].isdigit():
            return parts[1]
    
    return version


def compare_components(trivy_components: Dict[str, str], 
                      tcss_components: Dict[str, str]) -> Dict[str, List]:
    """
    比较两个扫描结果的差异
    """
    # 标准化版本号
    trivy_normalized = {name: normalize_version(version) 
                       for name, version in trivy_components.items()}
    tcss_normalized = {name: normalize_version(version) 
                      for name, version in tcss_components.items()}
    
    trivy_names = set(trivy_normalized.keys())
    tcss_names = set(tcss_normalized.keys())
    
    # 分类组件
    only_in_trivy = trivy_names - tcss_names
    only_in_tcss = tcss_names - trivy_names
    common_names = trivy_names & tcss_names
    
    # 在共同组件中找出版本不同的
    version_different = []
    version_same = []
    
    for name in common_names:
        trivy_ver = trivy_normalized[name]
        tcss_ver = tcss_normalized[name]
        
        if trivy_ver == tcss_ver:
            version_same.append((name, trivy_ver))
        else:
            version_different.append((name, trivy_ver, tcss_ver))
    
    return {
        'only_in_trivy': [(name, trivy_components[name]) for name in only_in_trivy],
        'only_in_tcss': [(name, tcss_components[name]) for name in only_in_tcss],
        'version_different': version_different,
        'version_same': version_same
    }


def print_analysis_report(comparison_result: Dict[str, List], 
                         trivy_total: int, tcss_total: int):
    """
    打印分析报告
    """
    print("=" * 80)
    print("镜像扫描结果差异分析报告")
    print("=" * 80)
    
    print(f"\n📊 总体统计:")
    print(f"  Trivy扫描到的组件总数: {trivy_total}")
    print(f"  TCSS扫描到的组件总数:  {tcss_total}")
    
    only_trivy = comparison_result['only_in_trivy']
    only_tcss = comparison_result['only_in_tcss']
    version_diff = comparison_result['version_different']
    version_same = comparison_result['version_same']
    
    print(f"  仅Trivy检测到:        {len(only_trivy)}")
    print(f"  仅TCSS检测到:         {len(only_tcss)}")
    print(f"  两者都检测到(版本相同): {len(version_same)}")
    print(f"  两者都检测到(版本不同): {len(version_diff)}")
    
    # 详细列表
    if only_trivy:
        print(f"\n🔍 仅Trivy检测到的组件 ({len(only_trivy)}个):")
        for name, version in sorted(only_trivy):
            print(f"  - {name} ({version})")
    
    if only_tcss:
        print(f"\n🔍 仅TCSS检测到的组件 ({len(only_tcss)}个):")
        for name, version in sorted(only_tcss):
            print(f"  - {name} ({version})")
    
    if version_diff:
        print(f"\n⚠️  版本不一致的组件 ({len(version_diff)}个):")
        for name, trivy_ver, tcss_ver in sorted(version_diff):
            print(f"  - {name}")
            print(f"    Trivy: {trivy_ver}")
            print(f"    TCSS:  {tcss_ver}")
    
    if version_same:
        print(f"\n✅ 版本一致的组件 ({len(version_same)}个):")
        # 只显示前10个，避免输出过长
        display_count = min(10, len(version_same))
        for name, version in sorted(version_same)[:display_count]:
            print(f"  - {name} ({version})")
        if len(version_same) > display_count:
            print(f"  ... 还有 {len(version_same) - display_count} 个组件")


def main():
    """主函数"""
    trivy_file = "trivy_result.json"
    tcss_file = "tcss_result.json"
    
    print("开始分析镜像扫描结果差异...")
    
    # 解析扫描结果
    print("正在解析Trivy扫描结果...")
    trivy_components = parse_trivy_results(trivy_file)
    
    print("正在解析TCSS扫描结果...")
    tcss_components = parse_tcss_results(tcss_file)
    
    if not trivy_components and not tcss_components:
        print("❌ 无法解析任何扫描结果，请检查文件格式")
        return
    
    # 比较差异
    print("正在比较差异...")
    comparison_result = compare_components(trivy_components, tcss_components)
    
    # 输出报告
    print_analysis_report(comparison_result, len(trivy_components), len(tcss_components))
    
    print("\n分析完成！")


if __name__ == "__main__":
    main()
