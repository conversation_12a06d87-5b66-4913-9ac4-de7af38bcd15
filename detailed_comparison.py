#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Trivy vs TCSS 详细比较分析工具
"""

import json
import re
from collections import defaultdict
from typing import Dict, Set, List, Tuple


def parse_trivy_results(trivy_file: str) -> Dict[str, Dict]:
    """解析Trivy SPDX格式的扫描结果，返回详细信息"""
    components = {}
    
    try:
        with open(trivy_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        packages = data.get('packages', [])
        
        for package in packages:
            name = package.get('name', '')
            version = package.get('versionInfo', '')
            pkg_type = 'unknown'
            
            if package.get('primaryPackagePurpose') == 'CONTAINER':
                continue
                
            if not name or not version:
                continue
            
            # 确定包类型
            external_refs = package.get('externalRefs', [])
            for ref in external_refs:
                if ref.get('referenceType') == 'purl':
                    purl = ref.get('referenceLocator', '')
                    if 'pkg:rpm/' in purl:
                        pkg_type = 'rpm'
                    elif 'pkg:pypi/' in purl:
                        pkg_type = 'python'
                    elif 'pkg:maven/' in purl:
                        pkg_type = 'java'
                    elif 'pkg:oci/' in purl:
                        pkg_type = 'container'
                    break
            
            # 处理Maven格式的包名
            original_name = name
            if ':' in name and name.count(':') == 1:
                name = name.split(':')[1]
            
            components[name] = {
                'version': version,
                'type': pkg_type,
                'original_name': original_name,
                'source': 'trivy'
            }
            
    except Exception as e:
        print(f"解析Trivy结果时出错: {e}")
        return {}
    
    return components


def parse_tcss_results(tcss_file: str) -> Dict[str, Dict]:
    """解析TCSS格式的扫描结果，返回详细信息"""
    components = {}
    
    try:
        with open(tcss_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if data.get('code') == 0 and data.get('data'):
            scan_data = data['data'][0] if isinstance(data['data'], list) else data['data']
            component_list = scan_data.get('components', [])
            
            for component in component_list:
                name = component.get('name', '')
                version = component.get('version', '')
                pkg_type = component.get('type', 'unknown')
                
                if not name or not version:
                    continue
                
                components[name] = {
                    'version': version,
                    'type': pkg_type,
                    'original_name': name,
                    'source': 'tcss'
                }
                
    except Exception as e:
        print(f"解析TCSS结果时出错: {e}")
        return {}
    
    return components


def categorize_components(components: Dict[str, Dict]) -> Dict[str, List]:
    """按类型分类组件"""
    categories = defaultdict(list)
    
    for name, info in components.items():
        pkg_type = info['type']
        version = info['version']
        
        # 更细致的分类
        if pkg_type == 'rpm':
            categories['System Packages (RPM)'].append((name, version))
        elif pkg_type == 'python' or pkg_type == 'python-pkg':
            categories['Python Packages'].append((name, version))
        elif pkg_type == 'java' or pkg_type == 'jar':
            categories['Java/Maven Packages'].append((name, version))
        elif 'tomcat' in name.lower():
            categories['Tomcat Components'].append((name, version))
        elif any(java_indicator in name.lower() for java_indicator in ['javax', 'org.', 'com.']):
            categories['Java Libraries'].append((name, version))
        elif name.lower().endswith(('.jar', '.war')):
            categories['Java Archives'].append((name, version))
        elif any(jdk_comp in name for jdk_comp in ['jce', 'jsse', 'nashorn', 'sunec']):
            categories['JDK Components'].append((name, version))
        else:
            categories['Other'].append((name, version))
    
    return dict(categories)


def normalize_version(version: str) -> str:
    """标准化版本号"""
    if not version:
        return ""
    
    if ':' in version:
        parts = version.split(':', 1)
        if len(parts) == 2 and parts[0].isdigit():
            return parts[1]
    
    return version


def detailed_comparison():
    """详细比较分析"""
    print("=" * 80)
    print("Trivy vs TCSS 详细比较分析报告")
    print("=" * 80)
    
    # 解析两个文件
    print("正在解析扫描结果...")
    trivy_components = parse_trivy_results("trivy_result.json")
    tcss_components = parse_tcss_results("tcss_result.json")
    
    print(f"Trivy检测到的组件: {len(trivy_components)}个")
    print(f"TCSS检测到的组件: {len(tcss_components)}个")
    
    # 基本差异分析
    trivy_names = set(trivy_components.keys())
    tcss_names = set(tcss_components.keys())
    
    only_trivy = trivy_names - tcss_names
    only_tcss = tcss_names - trivy_names
    common = trivy_names & tcss_names
    
    print(f"\n📊 基本统计:")
    print(f"  仅Trivy检测到: {len(only_trivy)}个")
    print(f"  仅TCSS检测到: {len(only_tcss)}个")
    print(f"  两者都检测到: {len(common)}个")
    
    # 按类型分析仅Trivy检测到的组件
    print(f"\n🔍 仅Trivy检测到的组件分类分析:")
    trivy_only_components = {name: trivy_components[name] for name in only_trivy}
    trivy_categories = categorize_components(trivy_only_components)
    
    for category, components in trivy_categories.items():
        print(f"  {category}: {len(components)}个")
        if len(components) <= 10:
            for name, version in sorted(components):
                print(f"    - {name} ({version})")
        else:
            for name, version in sorted(components)[:5]:
                print(f"    - {name} ({version})")
            print(f"    ... 还有 {len(components) - 5} 个组件")
    
    # 按类型分析仅TCSS检测到的组件
    print(f"\n🔍 仅TCSS检测到的组件分类分析:")
    tcss_only_components = {name: tcss_components[name] for name in only_tcss}
    tcss_categories = categorize_components(tcss_only_components)
    
    for category, components in tcss_categories.items():
        print(f"  {category}: {len(components)}个")
        if len(components) <= 10:
            for name, version in sorted(components):
                print(f"    - {name} ({version})")
        else:
            for name, version in sorted(components)[:5]:
                print(f"    - {name} ({version})")
            print(f"    ... 还有 {len(components) - 5} 个组件")
    
    # 版本差异分析
    print(f"\n⚠️  版本差异分析:")
    version_diffs = []
    for name in common:
        trivy_ver = trivy_components[name]['version']
        tcss_ver = tcss_components[name]['version']
        
        # 标准化版本比较
        trivy_normalized = normalize_version(trivy_ver)
        tcss_normalized = normalize_version(tcss_ver)
        
        if trivy_normalized != tcss_normalized:
            version_diffs.append((name, trivy_ver, tcss_ver))
    
    print(f"  发现 {len(version_diffs)} 个组件版本不一致")
    for name, trivy_ver, tcss_ver in sorted(version_diffs):
        print(f"    {name}: Trivy({trivy_ver}) vs TCSS({tcss_ver})")
    
    # 工具特征分析
    print(f"\n🔧 工具特征分析:")
    
    # Trivy特征
    trivy_java_count = len([name for name in only_trivy if any(indicator in name.lower() for indicator in ['java', 'tomcat', 'javax', 'org.', 'com.'])])
    trivy_python_count = len([name for name in only_trivy if trivy_components[name]['type'] in ['python', 'python-pkg']])
    
    print(f"  Trivy特征:")
    print(f"    - 更擅长检测Java/Maven组件: {trivy_java_count}个")
    print(f"    - 检测到的Python包: {trivy_python_count}个")
    print(f"    - 包含详细的Tomcat子组件")
    
    # TCSS特征
    tcss_jdk_count = len([name for name in only_tcss if any(jdk_comp in name for jdk_comp in ['jce', 'jsse', 'nashorn', 'sunec', 'examples'])])
    tcss_maven_format = len([name for name in only_tcss if ':' in name and name.count(':') >= 1])
    
    print(f"  TCSS特征:")
    print(f"    - 检测到JDK内置组件: {tcss_jdk_count}个")
    print(f"    - 使用Maven坐标格式: {tcss_maven_format}个")
    print(f"    - 包含JDK示例程序和工具")


def main():
    """主函数"""
    detailed_comparison()
    print("\n详细比较分析完成！")


if __name__ == "__main__":
    main()
