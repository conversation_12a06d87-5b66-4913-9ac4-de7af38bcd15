#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCSS扫描结果比较工具
比较tcss_old_result.json和tcss_result.json的差异
"""

import json
from typing import Dict, Set, List, Tuple


def parse_tcss_file(file_path: str) -> Dict[str, str]:
    """
    解析TCSS结果文件
    返回: {组件名: 版本} 的字典
    """
    components = {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if data.get('code') == 0 and data.get('data'):
            scan_data = data['data'][0] if isinstance(data['data'], list) else data['data']
            component_list = scan_data.get('components', [])
            
            for component in component_list:
                name = component.get('name', '')
                version = component.get('version', '')
                
                if not name or not version:
                    continue
                
                components[name] = version
                
    except Exception as e:
        print(f"解析{file_path}时出错: {e}")
        return {}
    
    return components


def normalize_version(version: str) -> str:
    """标准化版本号，去除epoch等前缀"""
    if not version:
        return ""
    
    # 处理epoch格式 (如 "1:7.5-21.tl2" -> "7.5-21.tl2")
    if ':' in version:
        parts = version.split(':', 1)
        if len(parts) == 2 and parts[0].isdigit():
            return parts[1]
    
    return version


def compare_tcss_results():
    """比较两个TCSS扫描结果"""
    print("=" * 80)
    print("TCSS扫描结果比较分析")
    print("=" * 80)
    
    # 解析两个文件
    print("正在解析tcss_old_result.json...")
    old_components = parse_tcss_file("tcss_old_result.json")
    
    print("正在解析tcss_result.json...")
    new_components = parse_tcss_file("tcss_result.json")
    
    if not old_components and not new_components:
        print("❌ 无法解析任何文件")
        return
    
    print(f"旧版本检测到的组件数量: {len(old_components)}")
    print(f"新版本检测到的组件数量: {len(new_components)}")
    
    # 标准化版本号进行比较
    old_normalized = {name: normalize_version(version) 
                     for name, version in old_components.items()}
    new_normalized = {name: normalize_version(version) 
                     for name, version in new_components.items()}
    
    old_names = set(old_normalized.keys())
    new_names = set(new_normalized.keys())
    
    # 分类差异
    only_in_old = old_names - new_names
    only_in_new = new_names - old_names
    common_names = old_names & new_names
    
    # 在共同组件中找出版本不同的
    version_different = []
    version_same = []
    
    for name in common_names:
        old_ver = old_normalized[name]
        new_ver = new_normalized[name]
        
        if old_ver == new_ver:
            version_same.append((name, old_ver))
        else:
            version_different.append((name, old_ver, new_ver))
    
    # 输出分析结果
    print(f"\n📊 差异统计:")
    print(f"  仅在旧版本中存在: {len(only_in_old)}个")
    print(f"  仅在新版本中存在: {len(only_in_new)}个")
    print(f"  两者都存在(版本相同): {len(version_same)}个")
    print(f"  两者都存在(版本不同): {len(version_different)}个")
    
    # 详细列表
    if only_in_old:
        print(f"\n🔍 仅在旧版本中存在的组件 ({len(only_in_old)}个):")
        for name in sorted(only_in_old):
            version = old_components[name]
            print(f"  - {name} ({version})")
    
    if only_in_new:
        print(f"\n🔍 仅在新版本中存在的组件 ({len(only_in_new)}个):")
        for name in sorted(only_in_new):
            version = new_components[name]
            print(f"  - {name} ({version})")
    
    if version_different:
        print(f"\n⚠️  版本不一致的组件 ({len(version_different)}个):")
        for name, old_ver, new_ver in sorted(version_different):
            print(f"  - {name}")
            print(f"    旧版本: {old_components[name]}")
            print(f"    新版本: {new_components[name]}")
    
    # 分析变化趋势
    print(f"\n📈 变化趋势分析:")
    total_change = len(only_in_old) + len(only_in_new) + len(version_different)
    total_components = len(old_names | new_names)
    change_rate = (total_change / total_components * 100) if total_components > 0 else 0
    
    print(f"  总变化率: {change_rate:.2f}%")
    print(f"  组件增减: {len(new_components) - len(old_components):+d}")
    
    if len(new_components) > len(old_components):
        print(f"  趋势: 新版本检测到更多组件")
    elif len(new_components) < len(old_components):
        print(f"  趋势: 新版本检测到更少组件")
    else:
        print(f"  趋势: 组件数量保持不变")
    
    # 分析重要组件的变化
    print(f"\n🔍 重要组件变化分析:")
    
    # 检查一些关键组件
    key_components = ['openssl', 'glibc', 'kernel', 'java', 'python', 'perl', 'gcc', 'mysql']
    
    for key_comp in key_components:
        old_matches = [name for name in old_names if key_comp.lower() in name.lower()]
        new_matches = [name for name in new_names if key_comp.lower() in name.lower()]
        
        if old_matches or new_matches:
            print(f"  {key_comp.upper()}相关组件:")
            print(f"    旧版本: {len(old_matches)}个")
            print(f"    新版本: {len(new_matches)}个")
            
            # 显示具体变化
            old_set = set(old_matches)
            new_set = set(new_matches)
            
            added = new_set - old_set
            removed = old_set - new_set
            
            if added:
                print(f"    新增: {', '.join(sorted(added))}")
            if removed:
                print(f"    移除: {', '.join(sorted(removed))}")
    
    print(f"\n📋 总结:")
    if change_rate < 5:
        print(f"  变化程度: 轻微变化")
        print(f"  影响评估: 两个版本的扫描结果基本一致")
    elif change_rate < 15:
        print(f"  变化程度: 中等变化")
        print(f"  影响评估: 存在一定差异，建议关注变化的组件")
    else:
        print(f"  变化程度: 显著变化")
        print(f"  影响评估: 两个版本差异较大，需要详细分析变化原因")
    
    print(f"  建议: 重点关注新增和移除的组件，确认是否符合预期")


def main():
    """主函数"""
    compare_tcss_results()
    print("\n比较分析完成！")


if __name__ == "__main__":
    main()
