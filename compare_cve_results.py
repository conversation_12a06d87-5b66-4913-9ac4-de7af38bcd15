#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Trivy vs TCSS CVE检测结果对比分析工具
"""

import json
import re
from collections import defaultdict
from typing import Dict, Set, List, Tu<PERSON>


def parse_trivy_html_vulnerabilities(html_file: str) -> Dict[str, List[Dict]]:
    """
    解析Trivy HTML漏洞报告
    返回: {组件名: [漏洞信息列表]} 的字典
    """
    vulnerabilities = {}

    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 使用正则表达式匹配表格行
        row_pattern = r'<tr class="severity-(\w+)">(.*?)</tr>'
        rows = re.findall(row_pattern, content, re.DOTALL)

        for severity, row_content in rows:
            # 提取包名
            pkg_match = re.search(r'<td class="pkg-name">([^<]+)</td>', row_content)
            # 提取CVE ID
            cve_match = re.search(r'<td>([^<]*CVE-[^<]*)</td>', row_content)
            # 提取版本
            version_match = re.search(r'<td class="pkg-version">([^<]+)</td>', row_content)

            if pkg_match and cve_match:
                pkg_name = pkg_match.group(1).strip()
                cve_id = cve_match.group(1).strip()
                version = version_match.group(1).strip() if version_match else 'unknown'

                if pkg_name not in vulnerabilities:
                    vulnerabilities[pkg_name] = []

                vulnerabilities[pkg_name].append({
                    'cve_id': cve_id,
                    'severity': severity.upper(),
                    'version': version
                })

    except Exception as e:
        print(f"解析Trivy HTML报告时出错: {e}")
        return {}

    return vulnerabilities


def parse_tcss_vulnerabilities(tcss_file: str) -> Dict[str, List[Dict]]:
    """
    解析TCSS JSON中的漏洞信息
    返回: {组件名: [漏洞信息列表]} 的字典
    """
    vulnerabilities = {}

    try:
        with open(tcss_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        if data.get('code') == 0 and data.get('data'):
            scan_data = data['data'][0] if isinstance(data['data'], list) else data['data']

            # 从vuln.list中获取漏洞信息
            vuln_data = scan_data.get('vuln', {})
            vuln_list = vuln_data.get('list', [])

            if vuln_list:
                for vuln in vuln_list:
                    component = vuln.get('component', '')
                    cve_id = vuln.get('cve_id', '')
                    level = vuln.get('level', 'UNKNOWN')
                    version = vuln.get('version', '')

                    if not component or not cve_id:
                        continue

                    # 标准化严重程度
                    severity_map = {
                        '低危': 'LOW',
                        '中危': 'MEDIUM',
                        '高危': 'HIGH',
                        '严重': 'CRITICAL'
                    }
                    severity = severity_map.get(level, level.upper())

                    if component not in vulnerabilities:
                        vulnerabilities[component] = []

                    vulnerabilities[component].append({
                        'cve_id': cve_id,
                        'severity': severity,
                        'version': version,
                        'level': level,
                        'name': vuln.get('name', ''),
                        'reference': vuln.get('reference', ''),
                        'vul_risk_score': vuln.get('vul_risk_score', ''),
                        'category': vuln.get('category', ''),
                        'category_type': vuln.get('category_type', '')
                    })

    except Exception as e:
        print(f"解析TCSS结果时出错: {e}")
        return {}

    return vulnerabilities


def extract_all_cves(vulnerabilities: Dict[str, List[Dict]]) -> Set[str]:
    """从漏洞信息中提取所有CVE ID"""
    cves = set()
    for pkg_vulns in vulnerabilities.values():
        for vuln in pkg_vulns:
            cve_id = vuln.get('cve_id', '')
            if cve_id and cve_id.startswith('CVE-'):
                cves.add(cve_id)
    return cves


def get_cve_to_packages(vulnerabilities: Dict[str, List[Dict]]) -> Dict[str, List[str]]:
    """获取CVE到受影响组件的映射"""
    cve_to_packages = defaultdict(list)
    for pkg_name, pkg_vulns in vulnerabilities.items():
        for vuln in pkg_vulns:
            cve_id = vuln.get('cve_id', '')
            if cve_id and cve_id.startswith('CVE-'):
                cve_to_packages[cve_id].append(pkg_name)
    return dict(cve_to_packages)


def compare_cve_results():
    """比较Trivy和TCSS的CVE检测结果"""
    print("=" * 80)
    print("Trivy vs TCSS CVE检测结果对比分析")
    print("=" * 80)

    # 解析漏洞信息
    print("正在解析Trivy HTML漏洞报告...")
    trivy_vulns = parse_trivy_html_vulnerabilities("ocloud-tcenter-mc-cas-portal.html")

    print("正在解析TCSS JSON漏洞信息...")
    tcss_vulns = parse_tcss_vulnerabilities("tcss_result.json")

    # 提取CVE列表
    trivy_cves = extract_all_cves(trivy_vulns)
    tcss_cves = extract_all_cves(tcss_vulns)

    print(f"\n📊 CVE检测统计:")
    print(f"  Trivy检测到的CVE数量: {len(trivy_cves)}")
    print(f"  TCSS检测到的CVE数量: {len(tcss_cves)}")
    print(f"  Trivy检测到的漏洞组件: {len(trivy_vulns)}")
    print(f"  TCSS检测到的漏洞组件: {len(tcss_vulns)}")

    # CVE差异分析
    only_trivy_cves = trivy_cves - tcss_cves
    only_tcss_cves = tcss_cves - trivy_cves
    common_cves = trivy_cves & tcss_cves

    print(f"\n🔍 CVE差异分析:")
    print(f"  仅Trivy检测到的CVE: {len(only_trivy_cves)}个")
    print(f"  仅TCSS检测到的CVE: {len(only_tcss_cves)}个")
    print(f"  两者都检测到的CVE: {len(common_cves)}个")

    # 获取CVE到组件的映射
    trivy_cve_to_pkg = get_cve_to_packages(trivy_vulns)
    tcss_cve_to_pkg = get_cve_to_packages(tcss_vulns)

    # 详细分析仅Trivy检测到的CVE
    if only_trivy_cves:
        print(f"\n🔍 仅Trivy检测到的CVE详情 ({len(only_trivy_cves)}个):")
        trivy_only_sorted = sorted(only_trivy_cves)

        # 按严重程度分类
        severity_count = {'CRITICAL': 0, 'HIGH': 0, 'MEDIUM': 0, 'LOW': 0, 'UNKNOWN': 0}

        for cve in trivy_only_sorted[:20]:  # 只显示前20个
            affected_pkgs = trivy_cve_to_pkg.get(cve, [])
            print(f"  {cve}:")
            for pkg in affected_pkgs:
                pkg_vulns = trivy_vulns.get(pkg, [])
                for vuln in pkg_vulns:
                    if vuln['cve_id'] == cve:
                        severity = vuln['severity']
                        severity_count[severity] += 1
                        print(f"    - {pkg} ({vuln['version']}) [{severity}]")

        if len(only_trivy_cves) > 20:
            print(f"  ... 还有 {len(only_trivy_cves) - 20} 个CVE")

        print(f"\n  仅Trivy检测CVE的严重程度分布:")
        for severity, count in severity_count.items():
            if count > 0:
                print(f"    {severity}: {count}个")

    # 详细分析仅TCSS检测到的CVE
    if only_tcss_cves:
        print(f"\n🔍 仅TCSS检测到的CVE详情 ({len(only_tcss_cves)}个):")
        tcss_only_sorted = sorted(only_tcss_cves)

        for cve in tcss_only_sorted[:20]:  # 只显示前20个
            affected_pkgs = tcss_cve_to_pkg.get(cve, [])
            print(f"  {cve}:")
            for pkg in affected_pkgs:
                pkg_vulns = tcss_vulns.get(pkg, [])
                for vuln in pkg_vulns:
                    if vuln['cve_id'] == cve:
                        print(f"    - {pkg} ({vuln['version']}) [{vuln.get('severity', 'UNKNOWN')}]")

        if len(only_tcss_cves) > 20:
            print(f"  ... 还有 {len(only_tcss_cves) - 20} 个CVE")

    # 共同检测到的CVE分析
    if common_cves:
        print(f"\n✅ 两者都检测到的CVE ({len(common_cves)}个):")
        common_sorted = sorted(common_cves)
        for cve in common_sorted[:10]:  # 只显示前10个
            trivy_pkgs = trivy_cve_to_pkg.get(cve, [])
            tcss_pkgs = tcss_cve_to_pkg.get(cve, [])
            print(f"  {cve}:")
            print(f"    Trivy: {', '.join(trivy_pkgs)}")
            print(f"    TCSS:  {', '.join(tcss_pkgs)}")

        if len(common_cves) > 10:
            print(f"  ... 还有 {len(common_cves) - 10} 个共同CVE")

    # 总体评估
    total_unique_cves = len(trivy_cves | tcss_cves)
    coverage_overlap = len(common_cves) / total_unique_cves * 100 if total_unique_cves > 0 else 0

    print(f"\n📈 总体评估:")
    print(f"  总计发现的唯一CVE: {total_unique_cves}个")
    print(f"  两工具检测重叠率: {coverage_overlap:.1f}%")

    if len(only_trivy_cves) > len(only_tcss_cves):
        print(f"  Trivy检测到更多独有CVE ({len(only_trivy_cves)} vs {len(only_tcss_cves)})")
    elif len(only_tcss_cves) > len(only_trivy_cves):
        print(f"  TCSS检测到更多独有CVE ({len(only_tcss_cves)} vs {len(only_trivy_cves)})")
    else:
        print(f"  两工具检测到的独有CVE数量相当")

    print(f"\n💡 建议:")
    if coverage_overlap < 70:
        print(f"  - 检测重叠率较低，强烈建议结合使用两种工具")
    elif coverage_overlap < 85:
        print(f"  - 检测重叠率中等，建议结合使用以获得更全面的覆盖")
    else:
        print(f"  - 检测重叠率较高，但仍建议定期使用两种工具进行交叉验证")

    print(f"  - 重点关注仅一方检测到的高危CVE")
    print(f"  - 建立CVE监控和跟踪机制")


def main():
    """主函数"""
    compare_cve_results()
    print("\nCVE对比分析完成！")


if __name__ == "__main__":
    main()
